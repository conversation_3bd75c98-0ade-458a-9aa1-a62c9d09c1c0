use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::repayment_log::{RepaymentLogCreate, RepaymentLogUpdate},
    entities::repayment_log::{RepaymentLog, RepaymentLogBmc},
};
use anyhow::anyhow;

pub struct RepaymentLogService;

impl RepaymentLogService {
    /// 获取还款记录总数
    /// # 参数
    /// * `req` - 查询条件，可选
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match RepaymentLogBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    /// 获取还款记录列表
    /// # 参数
    /// * `req` - 列表查询参数
    pub async fn get_list(req: ListParams) -> AppResult<Vec<RepaymentLog>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = RepaymentLogBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    /// 根据查询条件获取单个还款记录
    /// # 参数
    /// * `params` - 查询条件
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<RepaymentLog> {
        match RepaymentLogBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("RepaymentLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 根据ID获取还款记录
    /// # 参数
    /// * `id` - 还款记录ID
    pub async fn get_by_id(id: String) -> AppResult<RepaymentLog> {
        match RepaymentLogBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("RepaymentLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 创建新的还款记录
    /// # 参数
    /// * `req` - 创建还款记录的数据
    pub async fn create(req: RepaymentLogCreate) -> AppResult<String> {
        let res = RepaymentLogBmc::create(req).await?;
        Ok(res)
    }

    /// 更新还款记录
    /// # 参数
    /// * `req` - 更新还款记录的数据
    pub async fn update(req: RepaymentLogUpdate) -> AppResult<String> {
        RepaymentLogBmc::update(req).await?;
        Ok("RepaymentLog updated".to_string())
    }

    /// 删除还款记录
    /// # 参数
    /// * `id` - 要删除的还款记录ID
    pub async fn delete(id: String) -> AppResult<String> {
        RepaymentLogBmc::delete(id).await?;
        Ok("RepaymentLog deleted".to_string())
    }
}