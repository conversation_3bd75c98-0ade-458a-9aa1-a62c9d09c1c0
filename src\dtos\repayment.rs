use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

// 还款统计信息
#[derive(Default, Clone, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RepaymentCreate {
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<String>,       // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 需要偿还的本金
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 需要偿还的本金
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）
    pub loan_date: Option<String>,         // 贷款开始计算日期
    pub begin_date: Option<String>,        // 第一笔还款日期
    pub end_date: Option<String>,          // 最后一笔还款日期
    pub repay_status: Option<String>,      // 还款状态
    pub remark: Option<String>,            // 备注
    pub status: Option<String>,            // 状态
    pub creator_id: Option<String>,        // 创建人ID
    pub updater_id: Option<String>,        // 更新人ID
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for RepaymentCreate {}

#[derive(Default, Clone, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RepaymentUpdate {
    #[salvo(extract(source(from = "param")))]
    pub id: String,
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<String>,       // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 需要偿还的本金
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 需要偿还的本金
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）
    pub loan_date: Option<String>,         // 贷款开始计算日期
    pub begin_date: Option<String>,        // 第一笔还款日期
    pub end_date: Option<String>,          // 最后一笔还款日期
    pub repay_status: Option<String>,      // 还款状态
    pub remark: Option<String>,            // 备注
    pub status: Option<String>,            // 状态
    pub creator_id: Option<String>,        // 创建人ID
    pub updater_id: Option<String>,        // 更新人ID
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for RepaymentUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct RepaymentResponse {
    pub id: String,
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<String>,       // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 需要偿还的本金
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 需要偿还的本金
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）
    pub loan_date: Option<String>,         // 贷款开始计算日期
    pub begin_date: Option<String>,        // 第一笔还款日期
    pub end_date: Option<String>,          // 最后一笔还款日期
    pub repay_status: Option<String>,      // 还款状态
    pub remark: Option<String>,            // 备注
    pub status: Option<String>,            // 状态
    pub creator_id: Option<String>,        // 创建人ID
    pub updater_id: Option<String>,        // 更新人ID
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for RepaymentResponse {}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RelateOrders {
    pub id: String,
    pub ids: Vec<String>,
}
