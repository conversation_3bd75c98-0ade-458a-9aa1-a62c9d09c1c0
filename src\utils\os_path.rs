use md5;
use path_slash::PathBufExt;

pub fn check_path(obj_in: String) -> String {
    let mut p = std::path::PathBuf::new();
    p.push(obj_in);
    let res = PathBufExt::to_slash_lossy(&p).to_string();
    res
}

pub fn md5_string(obj_in: String) -> String {
    use std::fmt::Write;
    let md5digest = md5::compute(obj_in).to_vec();
    let mut obj_out = String::new();
    for a in md5digest.iter() {
        write!(obj_out, "{:02x}", a).unwrap();
    }
    obj_out
}
