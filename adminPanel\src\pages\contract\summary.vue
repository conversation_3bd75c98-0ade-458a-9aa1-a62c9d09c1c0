<template>
    <div  v-if="itemDetail?.id"  class="q-pa-md">
      <div class="row q-ma-sm">
            <div class="text-h5 col" style="margin-bottom: 10px">
              {{ itemDetail.name }}
              <q-chip
                square
                outline
                text-color="white"
                :color="getStatusColor(itemDetail.status)"
              >
                {{ getStatusText(itemDetail.status) }}
              </q-chip>
            </div>
            <div class="col-auto">
              <q-btn-group push>
                <q-btn
                  size="sm"
                  push
                  label="修改合同信息"
                  icon="edit"
                  color="secondary"
                  @click="editContractDetail"
                />
                <q-btn
                  size="sm"
                  push
                  label="查看附件"
                  icon="attachment"
                  color="orange"
                  @click="showAttachment"
                />
                <q-btn
                  size="sm"
                  push
                  label="打印合同"
                  icon="print"
                  color="primary"
                  @click="printContract"
                />
              </q-btn-group>
            </div>
          </div>
      <div class="row">
        <div class="col-6">
          <q-card>
        <q-card-section>
          <!-- 财务信息 -->
          <div class="text-h6 q-mb-md q-mt-lg">财务信息</div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">确认额度</div>
                <div class="col-8">
                  ¥{{ formatAmount(itemDetail.confirm_quota) }}
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">已用额度</div>
                <div class="col-8">
                  ¥{{ formatAmount(itemDetail.used_quota) }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">服务费率</div>
                <div class="col-8">{{ itemDetail.profit_calc_fee * 100 }}%</div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">计算周期</div>
                <div class="col-8">
                  {{ itemDetail.profit_calc_period }}
                </div>
              </div>
            </div>
          </div>
          <div class="row q-my-md">
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">违约金费率</div>
                <div class="col-8">
                  {{ itemDetail.penalty_calc_fee * 100 }} %
                </div>
              </div>
            </div>
            <div class="col">
              <div class="row">
                <div class="col-4 row-title">违约金计算周期</div>
                <div class="col-8">
                  {{ itemDetail.penalty_calc_period }}
                </div>
              </div>
            </div>
          </div>

        </q-card-section>
      </q-card>
        </div>
        <div class="col-6">
          <!-- 资金使用汇总情况 -->
        </div>
      </div>
      <!-- 资金使用历史情况 -->
      <div class="row">

      </div>

    </div>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";

// Props定义
const props = defineProps({
  itemId: {
    type: String,
    required: true
  },
  itemDetail: {
    type: Object,
    required: true
  }
});

const url = {
  item: "/api/financial_contract",
  delete: "/api/financial_contract",
  repayment: "/api/repayment/list",
  order: "/api/sales_order/list",
  totalPayment: "/api/sales_order/count_payment"
};

const itemDetail = ref();
const repaymentList = ref([]);
const unPayOrder = ref([]);
const unPayOrderTotal = ref(0);
const repaymentParams = ref({
    page: {
      page: 1,
      limit: 20,
    },
    options: {
      order_by: "created_at",
      desc  : true,
    },
    params: []
});
const unPayOrderParams = ref({
    page: {
      page: 1,
      limit: 20,
    },
    options: {
      order_by: "created_at",
      desc  : true,
    },
    params: []
});
// 状态字典
const statusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "new", label: "新建", color: "blue" },
  { value: "processing", label: "处理中", color: "orange" },
  { value: "done", label: "已完成", color: "green" },
  { value: "trash", label: "已废弃", color: "red" },
  { value: "expired", label: "已过期", color: "red" },
];

onMounted(async () => {
  if (props.itemId) {
    itemDetail.value = props.itemDetail;
    await getRepaymentList();
    await getUnPayOrder();
    await countUnPayOrder();
  } else {
    repaymentList.value = [];
    Notify.create({
      type: "warning",
      message: "还款信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatTimeStamp(datetime);
  };
});

// 获取状态颜色
const getStatusColor = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
const getStatusText = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString();
};

const getRepaymentList = async () => {
  repaymentParams.value.params.push({
    var: "contract_id",
    val: props.itemId
  });
  const { code, data } = await postAction(url.repayment, repaymentParams.value);
  if (code === 200) {
    repaymentList.value = data;
  } else {
    repaymentList.value = [];
    Notify.create({
      type: "warning",
      message: "还款信息查询失败，请重试",
      position: "top-right",
    });
  }
};

const getUnPayOrder = async () => {
  unPayOrderParams.value.params.push({
    var: "contract_id",
    val: props.itemId
  });
  unPayOrderParams.value.params.push({
    var: "raw",
    val: "repayment_id IS NULL"
  });
  const { code, data } = await postAction(url.order, unPayOrderParams.value);
  if (code === 200) {
    unPayOrder.value = data;
  } else {
    unPayOrder.value = [];
    Notify.create({
      type: "warning",
      message: "未还订单查询失败，请重试",
      position: "top-right",
    });
  }
};

const countUnPayOrder = async() => {
  let countParams = {
    page: {
      page: 1,
      limit: 20,
    },
    options: {
      order_by: "created_at",
      desc  : true,
    },
    params:  [
  {
    var: "contract_id",
    val: props.itemId
  },
  {
    var: "raw",
    val: "repayment_id IS NULL"
  }
  ]
};
  const { code, data } = await postAction(url.totalPayment, countParams);
  if (code === 200) {
    unPayOrderTotal.value = data;
  } else {
    unPayOrderTotal.value = 0;
    Notify.create({
      type: "warning",
      message: "未还订单统计失败，请重试",
      position: "top-right",
    });
  }
}
</script>

<style scoped lang="scss">
.row-title {
  font-weight: 600;
  color: #666;
  padding: 8px 0;
}

.contract-desc {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.text-h6 {
  color: #1976d2;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

:deep(img) {
  max-width: 100%;
}

@media print {
  .q-btn-group {
    display: none;
  }
}
</style>
