<template>
  <div v-if="itemDetail?.id" class="q-pa-md">
    <div class="row q-ma-sm">
      <div class="text-h5 col" style="margin-bottom: 10px">
        {{ itemDetail.name }}
        <q-chip square outline text-color="white" :color="getStatusColor(itemDetail.status)">
          {{ getStatusText(itemDetail.status) }}
        </q-chip>
      </div>
      <div class="col-auto">
        <q-btn-group push>
          <q-btn size="sm" push label="修改合同信息" icon="edit" color="secondary" @click="editContractDetail" />
          <q-btn size="sm" push label="查看附件" icon="attachment" color="orange" @click="showAttachment" />
          <q-btn size="sm" push label="打印合同" icon="print" color="primary" @click="printContract" />
        </q-btn-group>
      </div>
    </div>
    <div class="row">
      <div class="col-6 q-px-sm">
        <q-card>
          <q-card-section>
            <!-- 财务信息 -->
            <div class="text-h6 q-mb-md q-mt-lg">财务信息</div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">确认额度</div>
                  <div class="col-8">
                    ¥{{ formatAmount(itemDetail.confirm_quota) }}
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">已用额度</div>
                  <div class="col-8">
                    ¥{{ formatAmount(itemDetail.used_quota) }}
                  </div>
                </div>
              </div>
            </div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">服务费率</div>
                  <div class="col-8">{{ itemDetail.profit_calc_fee * 100 }}%</div>
                </div>
              </div>
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">计算周期</div>
                  <div class="col-8">
                    {{ itemDetail.profit_calc_period }}
                  </div>
                </div>
              </div>
            </div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">违约金费率</div>
                  <div class="col-8">
                    {{ itemDetail.penalty_calc_fee * 100 }} %
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">违约金计算周期</div>
                  <div class="col-8">
                    {{ itemDetail.penalty_calc_period }}
                  </div>
                </div>
              </div>
            </div>

          </q-card-section>
        </q-card>
      </div>
      <div class="col-6 q-px-sm">
        <!-- 资金使用汇总情况 -->
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md q-mt-lg">资金使用汇总情况</div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">订单总金额</div>
                  <div class="col-8">
                    ¥{{ formatAmount(unPayOrderTotal.total_amount) }}
                  </div>
                </div>
              </div>
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">已付款金额</div>
                  <div class="col-8">
                    ¥{{ formatAmount(unPayOrderTotal.paid_amount) }}
                  </div>
                </div>
              </div>
            </div>
            <div class="row q-my-md">
              <div class="col">
                <div class="row">
                  <div class="col-4 row-title">待付款金额</div>
                  <div class="col-8">
                    ¥{{ formatAmount(unPayOrderTotal.unpay_amount) }}
                  </div>
                </div>
              </div>
              <div class="col">
                <!-- 空列用于保持布局对称 -->
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    <!-- 资金使用历史情况 -->
    <div class="row q-my-md">
      <div class="col-12 q-px-sm">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md q-mt-lg">资金使用历史情况</div>

            <q-table v-model:pagination="repaymentPagination" row-key="id" separator="cell" :rows="repaymentTableData"
              :columns="repaymentColumns" :rows-per-page-options="repaymentPageOptions" :loading="repaymentLoading"
              @request="onRepaymentRequest" dense>

              <template #top="props">
                <q-btn color="primary" label="创建还款计划" @click="handleInitRepayment" />
                <q-space />
                <q-btn flat round dense :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md"
                  @click="props.toggleFullscreen" />
              </template>

              <template #body-cell-status="props">
                <q-td :props="props">
                  <q-chip :color="getRepayStatusColor(props.row.status)" text-color="white">
                    {{ getRepayStatusText(props.row.status) }}
                  </q-chip>
                </q-td>
              </template>

              <template #body-cell-actions="props">
                <q-td :props="props">
                  <div class="q-gutter-xs">
                    <q-btn-group>
                      <q-btn size="sm" color="primary" label="查看明细" @click="showRepaymentDetail(props.row)" />
                    </q-btn-group>
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>
      </div>
    </div>

  </div>
</template>

<script setup>
import { Notify } from "quasar";
import { postAction } from "src/api/manage";
import { FormatTimeStamp } from "src/utils/date";
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import useTableData from "src/composables/useTableData";

// Props定义
const props = defineProps({
  itemId: {
    type: String,
    required: true
  },
  itemDetail: {
    type: Object,
    required: true
  }
});

const router = useRouter();

const url = {
  item: "/api/financial_contract",
  delete: "/api/financial_contract",
  repayment: "/api/repayment/list",
  repayment_create: "/api/repayment",
  order: "/api/sales_order/list",
  totalPayment: "/api/sales_order/count_payment"
};

const itemDetail = ref();
const unPayOrder = ref([]);
const unPayOrderTotal = ref(0);

// 还款表格的分页状态
const repaymentPagination = ref({
  sortBy: 'created_at',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
});
const repaymentPageOptions = ref([10, 30, 50, 100]);
const repaymentLoading = ref(false);
const repaymentTableData = ref([]);
const unPayOrderParams = ref({
  page: {
    page: 1,
    limit: 20,
  },
  options: {
    order_by: "created_at",
    desc: true,
  },
  params: []
});
// 还款表格的columns定义，基于RepaymentResponse结构体
const repaymentColumns = computed(() => {
  return [
    { name: "serial", align: "center", label: "还款计划编号", field: "serial" },
    { name: "profit_predict", align: "center", label: "预计利润", field: "profit_predict", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "principal_predict", align: "center", label: "预计本金", field: "principal_predict", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "amount_predict", align: "center", label: "预计总额", field: "amount_predict", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "profit_actual", align: "center", label: "实际利润", field: "profit_actual", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "principal_actual", align: "center", label: "实际本金", field: "principal_actual", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "amount_actual", align: "center", label: "实际总额", field: "amount_actual", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "profit_remain", align: "center", label: "剩余利润", field: "profit_remain", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "principal_remain", align: "center", label: "剩余本金", field: "principal_remain", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "amount_remain", align: "center", label: "剩余总额", field: "amount_remain", format: val => val ? `¥${formatAmount(val)}` : '-' },
    { name: "begin_date", align: "center", label: "开始日期", field: "begin_date" },
    { name: "end_date", align: "center", label: "结束日期", field: "end_date" },
    { name: "repay_status", align: "center", label: "还款状态", field: "repay_status" },
    { name: "status", align: "center", label: "计划状态", field: "status" },
    { name: "created_at", align: "center", label: "创建时间", field: "created_at" },
    { name: "actions", align: "center", label: "操作", field: "actions" },
  ];
});

// 状态字典
const statusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "new", label: "新建", color: "blue" },
  { value: "processing", label: "处理中", color: "orange" },
  { value: "pending", label: "待还款", color: "orange" },
  { value: "partial", label: "部分还款", color: "orange" },
  { value: "completed", label: "已完成", color: "green" },
  { value: "overdue", label: "逾期", color: "red" },
];

// 还款表格的请求函数
const onRepaymentRequest = async (props) => {
  repaymentLoading.value = true;
  repaymentTableData.value = [];

  // 组装分页和过滤条件
  const allParams = {
    options: {
      order_by: props.pagination.sortBy,
      desc: props.pagination.descending,
    },
    page: {
      page: props.pagination.page,
      limit: props.pagination.rowsPerPage,
    },
    params: [
      {
        var: "contract_id",
        val: itemDetail.value?.id || props.itemId
      }
    ]
  };

  try {
    const { code, data } = await postAction(url.repayment, allParams);
    if (code === 200) {
      // 更新分页信息
      repaymentPagination.value = props.pagination;
      repaymentPagination.value.rowsNumber = data.total;
      repaymentTableData.value = data.data;
    } else {
      Notify.create({
        type: "warning",
        message: "还款信息查询失败，请重试",
        position: "top-right",
      });
    }
  } catch (error) {
    console.error('获取还款数据失败:', error);
    Notify.create({
      type: "negative",
      message: "获取还款数据失败",
      position: "top-right",
    });
  } finally {
    repaymentLoading.value = false;
  }
};

// 获取还款表格数据
const getRepaymentTableData = () => onRepaymentRequest({ pagination: repaymentPagination.value });

onMounted(async () => {
  if (props.itemId) {
    itemDetail.value = props.itemDetail;

    // 初始化还款表格并获取数据
    await getRepaymentTableData();
    await getUnPayOrder();
    await countUnPayOrder();
  } else {
    repaymentTableData.value = [];
    Notify.create({
      type: "warning",
      message: "还款信息查询失败，请重试",
      position: "top-right",
    });
  }
});

const showDateTime = computed(() => {
  return (datetime) => {
    return FormatTimeStamp(datetime);
  };
});

// 获取状态颜色
const getStatusColor = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
const getStatusText = (status) => {
  const statusItem = statusDict.find((item) => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 获取还款状态颜色
const getRepayStatusColor = (repayStatus) => {
  const statusItem = statusDict.find((item) => item.value === repayStatus);
  return statusItem ? statusItem.color : "grey";
};

// 获取还款状态文本
const getRepayStatusText = (repayStatus) => {
  const statusItem = statusDict.find((item) => item.value === repayStatus);
  return statusItem ? statusItem.label : (repayStatus || '未知');
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString();
};

const handleInitRepayment = async () => {
  // 构建RepaymentCreate数据结构
  const repaymentData = {
    serial: null,                    // 还款计划编号
    contract_id: props.itemId,     // 合作项目ID
    status: "draft",                 // 状态设置为草稿
  };

  // 使用url.create创建还款数据
  const res = await postAction(url.repayment_create, repaymentData);

  if (res.code === 200) {
    router.push({
      path: "/repayment/detail",
      query: { id: res.data },
    });
  } else {
    // 创建失败，使用默认数据
    Notify.create({
      type: "warning",
      message: "创建还款计划失败，请重试",
      position: "top-right",
    });
  }
};

const showRepaymentDetail = (row) => {
  router.push({
    path: "/repayment/detail",
    query: { id: row.id },
  });
};

const getUnPayOrder = async () => {
  unPayOrderParams.value.params.push({
    var: "contract_id",
    val: props.itemId
  });
  unPayOrderParams.value.params.push({
    var: "repayment_id",
    val: "repayment:None"
  });
  const { code, data } = await postAction(url.order, unPayOrderParams.value);
  if (code === 200) {
    unPayOrder.value = data;
  } else {
    unPayOrder.value = [];
    Notify.create({
      type: "warning",
      message: "未还订单查询失败，请重试",
      position: "top-right",
    });
  }
};

const countUnPayOrder = async () => {
  const { code, data } = await postAction(url.totalPayment, { id: props.itemId });
  if (code === 200) {
    unPayOrderTotal.value = data;
  } else {
    unPayOrderTotal.value = 0;
    Notify.create({
      type: "warning",
      message: "未还订单统计失败，请重试",
      position: "top-right",
    });
  }
}
</script>

<style scoped lang="scss">
.row-title {
  font-weight: 600;
  color: #666;
  padding: 8px 0;
}

.contract-desc {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}

.text-h6 {
  color: #1976d2;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

:deep(img) {
  max-width: 100%;
}

@media print {
  .q-btn-group {
    display: none;
  }
}
</style>
