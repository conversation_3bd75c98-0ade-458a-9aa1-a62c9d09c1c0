<template>
  <base-content scrollable>
    <div class="row q-gutter-md q-ma-md">
      <div class="col">
        <div class="items-center row q-gutter-md" style="margin-bottom: 10px">
          <q-input
            v-model="queryParams.serial"
            style="width: 20%"
            label="合同编号"
          />
          <q-input
            v-model="queryParams.name"
            style="width: 20%"
            label="合同名称"
          />
          <q-btn color="primary" label="搜索" @click="handleSearch" />
          <q-btn color="primary" label="重置" @click="resetSearch" />
        </div>
        <q-table
          v-model:pagination="pagination"
          row-key="id"
          separator="cell"
          :rows="tableData"
          :columns="columns"
          :rows-per-page-options="pageOptions"
          :loading="loading"
          @request="onRequest"
        >
          <template #top="props">
            <q-btn color="primary" label="创建合同" @click="createContract" />
            <q-space />
            <q-btn
              flat
              round
              dense
              :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
              class="q-ml-md"
              @click="props.toggleFullscreen"
            />
          </template>

          <template #body-cell-status="props">
            <q-td :props="props">
              <q-chip
                v-if="props.row.status === 'draft'"
                color="grey"
                text-color="white"
                >草稿</q-chip
              >
              <q-chip
                v-else-if="props.row.status === 'new'"
                color="blue"
                text-color="white"
                >待审核</q-chip
              >
              <q-chip
                v-else-if="props.row.status === 'processing'"
                color="orange"
                text-color="white"
                >已审核</q-chip
              >
              <q-chip
                v-else-if="props.row.status === 'done'"
                color="positive"
                text-color="white"
                >已完成</q-chip
              >
              <q-chip
                v-else-if="props.row.status === 'expired'"
                color="negative"
                text-color="white"
                >已过期</q-chip
              >
              <q-chip v-else>{{ props.row.status }}</q-chip>
            </q-td>
          </template>

          <template #body-cell-actions="props">
            <q-td :props="props">
              <div class="q-gutter-xs">
                <q-btn-group>
                  <q-btn
                    size="sm"
                    color="primary"
                    label="查看明细"
                    @click="showDetail(props.row)"
                  />
                </q-btn-group>
              </div>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
  </base-content>
</template>

<script setup>
defineOptions({ name: "UserList" });
import { useQuasar } from "quasar";
import BaseContent from "src/components/BaseContent/BaseContent.vue";
import useTableData from "src/composables/useTableData";
import { computed, onMounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

const url = {
  list: "/api/financial_contract/list",
  create: "/api/financial_contract",
  edit: "/api/financial_contract",
  delete: "/api/financial_contract",
};

const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleSearch,
} = useTableData(url);

onMounted(async () => {
  pagination.value.order = "created_at";
  pagination.value.descending = true;
  await getTableData();
});

const $q = useQuasar();
const columns = computed(() => {
  return [
    { name: "name", align: "center", label: "合同名称", field: "name" },
    {
      name: "serial",
      align: "center",
      label: "合同编号",
      field: "serial",
    },
    {
      name: "applier",
      align: "center",
      label: "申请人",
      field: "applier",
    },
    {
      name: "contract_type",
      align: "center",
      label: "合同类型",
      field: "contract_type",
    },
    {
      name: "begin_time",
      align: "center",
      label: "开始时间",
      field: "begin_time",
    },
    {
      name: "end_time",
      align: "center",
      label: "结束时间",
      field: "end_time",
    },
    {
      name: "funder",
      align: "center",
      label: "资金方",
      field: "funder",
    },
    {
      name: "status",
      align: "center",
      label: "合同状态",
      field: "status",
    },
    {
      name: "actions",
      align: "center",
      label: "操作",
      field: "actions",
    },
  ];
});
const createContract = () => {
  router.push({
    name: "contractCreate",
  });
};
const showDetail = (row) => {
  router.push({
    name: "contractDetail",
    query: { id: row.id },
  });
};
</script>
