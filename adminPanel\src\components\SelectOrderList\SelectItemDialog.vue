<template>
  <q-dialog v-model="selectItemVisible" position="right" persistent>
    <q-card style="min-width: 80vw; max-width: 80vw">
      <q-card-section class="row justify-between items-center">
        <div class="text-subtitle1">
          选择订单进行关联
        </div>
        <q-btn dense color="primary" label="选择" @click="handleSelectItem()" />
      </q-card-section>

      <q-separator />

      <q-card-section class="items-center row q-gutter-md">
        <q-input v-model="queryParams.name" dense style="width: 20%" label="名称" />
        <q-btn dense color="primary" label="搜索" @click="handleSearch" />
        <q-btn dense color="primary" label="重置" @click="resetSearch" />
      </q-card-section>

      <q-table v-model:pagination="pagination" v-model:selected="selected" row-key="id" :rows="tableData"
        :columns="columns" :rows-per-page-options="pageOptions" :loading="loading" selection="multiple"
        @request="onRequest">
        <template #body-cell-actions="props">
          <q-td :props="props">
            <div class="q-gutter-xs">
              <q-btn dense color="primary" label="选择" @click="handleSelectItem(props.row)" />
            </div>
          </q-td>
        </template>
      </q-table>
    </q-card>
  </q-dialog>
</template>

<script setup>
import useTableData from "src/composables/useTableData";
import { ArrayOrObject } from "src/utils/arrayOrObject";
import { computed, ref, toRefs } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  urlList: {
    type: String,
    required: true,
  },
  contractId: {
    type: String,
    required: true,
  },
});
const { urlList } = toRefs(props);
const columns = computed(() => {
  return [
    {
      name: "platform_order_serial",
      align: "center",
      label: "平台订单编号",
      field: "platform_order_serial",
    },
    {
      name: "platform_name",
      align: "center",
      label: "销售平台",
      field: "platform_name",
    },
    {
      name: "serial",
      required: true,
      align: "center",
      label: "订单编号",
      field: "serial",
    },
    {
      name: "total_payment",
      required: true,
      align: "center",
      label: "订单总额",
      field: "total_payment",
    },
    {
      name: "status",
      align: "center",
      label: "订单状态",
      field: "status",
    },
  ];
});
const url = computed(() => {
  return {
    list: urlList.value,
  };
});
const {
  pagination,
  queryParams,
  pageOptions,
  loading,
  tableData,
  onRequest,
  getTableData,
  handleSearch,
  resetSearch,
} = useTableData(url.value);

const selectItemVisible = ref(false);
const selected = ref(null);

const show = () => {
  selected.value = [];
  pagination.value.order_by = "created_at";
  pagination.value.descending = false;
  queryParams.value.repayment_id = "repayment:None";
  queryParams.value.contract_id = props.contractId;
  selectItemVisible.value = true;
  getTableData();
};

defineExpose({
  show,
});
const emit = defineEmits(["handleSelectItem"]);
const handleSelectItem = () => {
  if (selected.value.length) {
    emit("handleSelectItem", selected.value);
    console.log(selected.value);
  }
  selectItemVisible.value = false;
};
</script>
