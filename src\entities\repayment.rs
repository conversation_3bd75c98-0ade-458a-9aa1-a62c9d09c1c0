use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        UpdateOptions, WhereOptions,
    },
    dtos::repayment::{RepaymentCreate, RepaymentResponse, RepaymentUpdate},
    entities::financial_contract::CalcPeriod,
    utils::rand_utils::random_uppercase_serial,
};
use anyhow::anyhow;
use chrono::Local;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct Repayment {
    pub id: Option<RecordId>,
    pub serial: Option<String>,            // 还款计划编号
    pub contract_id: Option<RecordId>,     // 合同ID
    pub profit_predict: Option<String>,    // 需要偿还的利润
    pub principal_predict: Option<String>, // 需要偿还的本金（从合同获取的额度）
    pub amount_predict: Option<String>,    // 需要偿还的总额
    pub profit_actual: Option<String>,     // 实际偿还的利润
    pub principal_actual: Option<String>,  // 实际偿还的本金（从合同获取的额度）
    pub amount_actual: Option<String>,     // 实际偿还的总额
    pub profit_remain: Option<String>,     // 剩余需要偿还的利润
    pub principal_remain: Option<String>,  // 剩余需要偿还的本金（从合同获取的额度）
    pub amount_remain: Option<String>,     // 剩余需要偿还的总额
    pub target_amount: Option<String>,     // 目标费用总额（订单总金额）
    
    pub profit_calc_fee: Decimal,                 // 利润计算费用
    pub penalty_calc_fee: Decimal,                 // 违约金计算费用
    pub profit_calc_period: Option<CalcPeriod>,         // 计算周期
    pub penalty_calc_period: Option<CalcPeriod>, // 违约金计算方式

    pub begin_date: Option<String>,        // 额度支取的第一天，从这一天开始计算利润
    pub end_date: Option<String>,          // 约定偿还额度的最后一天，过了这一天就计算违约金
    pub repay_date: Option<String>,        // 实际还款日期

    pub remark: Option<String>,            // 备注
    
    pub status: Option<String>,            // 状态
    pub creator_id: Option<String>,        // 创建人ID
    pub updater_id: Option<String>,        // 更新人ID
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for Repayment {}
impl Patchable for Repayment {}
impl Castable for Repayment {}

impl Repayment {
    /// 将 Repayment 实体转换为响应 DTO
    pub fn response(self) -> RepaymentResponse {
        RepaymentResponse {
            id: self.id.unwrap().to_string(),
            serial: self.serial,
            contract_id: self.contract_id.map(|id| id.to_string()),
            profit_predict: self.profit_predict,
            principal_predict: self.principal_predict,
            amount_predict: self.amount_predict,
            profit_actual: self.profit_actual,
            principal_actual: self.principal_actual,
            amount_actual: self.amount_actual,
            profit_remain: self.profit_remain,
            principal_remain: self.principal_remain,
            amount_remain: self.amount_remain,
            target_amount: self.target_amount,
            profit_calc_fee: self.profit_calc_fee,
            penalty_calc_fee: self.penalty_calc_fee,
            profit_calc_period: self.profit_calc_period.map(|p| format!("{:?}", p)),
            penalty_calc_period: self.penalty_calc_period.map(|p| format!("{:?}", p)),
            begin_date: self.begin_date,
            end_date: self.end_date,
            repay_date: self.repay_date,
            remark: self.remark,
            status: self.status,
            creator_id: self.creator_id,
            updater_id: self.updater_id,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    /// 从创建 DTO 创建新的 Repayment 实体
    pub fn create(obj: RepaymentCreate) -> Repayment {
        let time_now: i64 = Local::now().timestamp_millis();
        let serial = obj
            .serial
            .unwrap_or_else(|| random_uppercase_serial(Some("RP".to_string()), 6));
        let contract_id = if obj.contract_id.is_some() {
            Some(RecordId::from_str(&obj.contract_id.unwrap()).unwrap())
        } else {
            None
        };
        Repayment {
            id: None,
            serial: Some(serial),
            contract_id: contract_id,
            profit_predict: obj.profit_predict,
            principal_predict: obj.principal_predict,
            amount_predict: obj.amount_predict,
            profit_actual: obj.profit_actual,
            principal_actual: obj.principal_actual,
            amount_actual: obj.amount_actual,
            profit_remain: obj.profit_remain,
            principal_remain: obj.principal_remain,
            amount_remain: obj.amount_remain,
            target_amount: obj.target_amount,
            profit_calc_fee: obj.profit_calc_fee,
            penalty_calc_fee: obj.penalty_calc_fee,
            profit_calc_period: obj.profit_calc_period.and_then(|p| CalcPeriod::from_str(&p)),
            penalty_calc_period: obj.penalty_calc_period.and_then(|p| CalcPeriod::from_str(&p)),
            begin_date: obj.begin_date,
            end_date: obj.end_date,
            repay_date: obj.repay_date,
            remark: obj.remark,
            status: obj.status, // 状态
            creator_id: obj.creator_id,
            updater_id: None, // 创建时不设置更新人ID
            created_at: time_now,
            updated_at: time_now,
        }
    }

    /// 从更新 DTO 更新 Repayment 实体
    pub fn update(mut self, obj: RepaymentUpdate) -> Repayment {
        let time_now: i64 = Local::now().timestamp_millis();

        // 只更新提供的字段
        if let Some(serial) = obj.serial {
            self.serial = Some(serial);
        }
        if let Some(contract_id) = obj.contract_id {
            self.contract_id =
                RecordId::from_str(&format!("financial_contract:{}", contract_id)).ok();
        }
        if let Some(profit_predict) = obj.profit_predict {
            self.profit_predict = Some(profit_predict);
        }
        if let Some(principal_predict) = obj.principal_predict {
            self.principal_predict = Some(principal_predict);
        }
        if let Some(amount_predict) = obj.amount_predict {
            self.amount_predict = Some(amount_predict);
        }
        if let Some(profit_actual) = obj.profit_actual {
            self.profit_actual = Some(profit_actual);
        }
        if let Some(principal_actual) = obj.principal_actual {
            self.principal_actual = Some(principal_actual);
        }
        if let Some(amount_actual) = obj.amount_actual {
            self.amount_actual = Some(amount_actual);
        }
        if let Some(profit_remain) = obj.profit_remain {
            self.profit_remain = Some(profit_remain);
        }
        if let Some(principal_remain) = obj.principal_remain {
            self.principal_remain = Some(principal_remain);
        }
        if let Some(amount_remain) = obj.amount_remain {
            self.amount_remain = Some(amount_remain);
        }
        if let Some(target_amount) = obj.target_amount {
            self.target_amount = Some(target_amount);
        }
        if let Some(profit_calc_fee) = obj.profit_calc_fee {
            self.profit_calc_fee = profit_calc_fee;
        }
        if let Some(penalty_calc_fee) = obj.penalty_calc_fee {
            self.penalty_calc_fee = penalty_calc_fee;
        }
        if let Some(profit_calc_period) = obj.profit_calc_period {
            self.profit_calc_period = CalcPeriod::from_str(&profit_calc_period);
        }
        if let Some(penalty_calc_period) = obj.penalty_calc_period {
            self.penalty_calc_period = CalcPeriod::from_str(&penalty_calc_period);
        }
        if let Some(begin_date) = obj.begin_date {
            self.begin_date = Some(begin_date);
        }
        if let Some(end_date) = obj.end_date {
            self.end_date = Some(end_date);
        }
        if let Some(repay_date) = obj.repay_date {
            self.repay_date = Some(repay_date);
        }
        if let Some(remark) = obj.remark {
            self.remark = Some(remark);
        }
        if let Some(status) = obj.status {
            self.status = Some(status);
        }
        if let Some(updater_id) = obj.updater_id {
            self.updater_id = Some(updater_id);
        }

        // 更新时间戳
        self.updated_at = time_now;

        self
    }
}

pub struct RepaymentBmc;

impl RepaymentBmc {
    const ENTITY: &'static str = "repayment";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Repayment>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Repayment>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<Repayment>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(repayment: RepaymentCreate) -> AppResult<String> {
        let obj = Repayment::create(repayment);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(repayment: RepaymentUpdate) -> AppResult<String> {
        let check: Option<Repayment> =
            Database::exec_get_by_id(Self::ENTITY, &repayment.id.clone().to_string()).await?;
        if check.is_none() {
            return Err(anyhow!("Repayment not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };
        let obj = Repayment::update(old, repayment);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        let check: Option<Repayment> =
            Database::exec_get_by_id(Self::ENTITY, &id).await?;
        if check.is_none() {
            return Err(anyhow!("Repayment not found.").into());
        }
        let check = check.unwrap();
        // 检查还款计划状态，只有草稿或新建状态才能删除
        if let Some(status) = &check.status {
            if status != "draft" && status != "new" {
                return Err(anyhow!("Cannot delete completed repayment.").into());
            }
        }
        if check.status.as_deref() != Some("draft") || check.status.as_deref() != Some("new") {
            return Err(anyhow!("Cannot delete completed repayment.").into());
        }
        // 删除还款计划前检查状态
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }

    /// 根据查询条件更新单个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "RP001".to_string())];
    /// let result = RepaymentBmc::update_field(params, "status", "completed").await?;
    /// ```
    pub async fn update_field(
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        Database::exec_update_by_query(Self::ENTITY, params, update_field, update_value).await
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let update_fields = vec![
    ///     UpdateOptions::new("status".to_string(), "completed".to_string()),
    ///     UpdateOptions::new("amount".to_string(), "1000.00".to_string()),
    /// ];
    /// let result = SalesOrderBmc::update_multiple_fields(params, update_fields).await?;
    /// ```
    pub async fn update_multiple_fields(
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        Database::exec_update_multiple_fields_by_query(Self::ENTITY, params, update_fields).await
    }
}
