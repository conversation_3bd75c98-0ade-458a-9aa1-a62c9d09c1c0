use crate::app_writer::AppResult;
use crate::config::CFG;
use salvo::prelude::{Extractible, ToSchema};
use serde::de::{self, Deserializer, Visitor};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use std::fmt;
use std::sync::LazyLock;
use surrealdb::engine::remote::ws::{Client, Ws};
use surrealdb::opt::auth::Root;
use surrealdb::{sql::Thing, Surreal};

const PAGE_LIMIT: u32 = 15;

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct Page {
    pub limit: u32,
    pub page: u32,
}

impl Page {
    pub fn default() -> Self {
        Page {
            limit: PAGE_LIMIT,
            page: 0,
        }
    }

    pub fn unlimited() -> Self {
        Page { limit: 0, page: 0 }
    }

    pub fn get_limit(&self) -> anyhow::Result<u32> {
        if self.limit < 1 {
            return Err(anyhow::anyhow!("page limit must be greater than 0"));
        }
        Ok(self.limit)
    }

    pub fn get_offset(&self) -> u32 {
        if self.page == 0 {
            return 0;
        }
        (self.page - 1) * self.limit
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct FlexibleParams {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub options: Option<ListOptions>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<IdParams>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub params: Option<Vec<WhereOptions>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub page: Option<Page>,
}

// 为FlexibleParams实现默认值
impl Default for FlexibleParams {
    fn default() -> Self {
        Self {
            options: None,
            id: None,
            params: None,
            page: None,
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct ListOptions {
    pub order_by: Option<String>,
    pub desc: Option<bool>,
}

impl ListOptions {
    pub fn default() -> Self {
        ListOptions {
            order_by: Some("created_at".to_string()),
            desc: Some(true),
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct WhereOptions {
    pub var: String,
    pub val: String,
}

impl WhereOptions {
    pub fn get_sql(&self) -> String {
        match self.var.as_str() {
            "id" => format!("{} = {}", self.var, self.val),
            "ids" => format!("id IN [{}]", self.val),
            "owner" => format!("owner = {}", self.val),
            "sender" => format!("sender = {}", self.val),
            "receiver" => format!("receiver = {}", self.val),
            "user" => format!("user = {}", self.val),
            "users" => format!("user IN [{}]", self.val),
            "role" => format!("role = {}", self.val),
            "group" => format!("group = {}", self.val),
            "roles" => format!("role IN [{}]", self.val),
            "groups" => format!("group IN [{}]", self.val),
            "usage" => format!("usage = {}", self.val),
            "usages" => format!("usage IN [{}]", self.val),
            "tag" => format!("tag = {}", self.val),
            "tags" => format!("tag IN [{}]", self.val),
            "activity" => format!("activity = {}", self.val),
            "activities" => format!("activity IN [{}]", self.val),
            "begin_date" => format!("date >= '{}'", self.val),
            "end_date" => format!("date <= '{}'", self.val),
            "ralate" | "raw" => self.val.clone(),
            _ if self.var.contains(".id") || self.var.contains("_id") => {
                format!("{} = {}", self.var, self.val)
            }
            _ => format!("{} = '{}'", self.var, self.val),
        }
    }

    pub fn new(var: String, val: String) -> Self {
        WhereOptions { var, val }
    }
}

/// 字段更新选项，用于多字段更新
#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct UpdateOptions {
    pub field: String,
    pub value: String,
}

impl UpdateOptions {
    /// 生成SQL SET子句
    pub fn get_sql(&self) -> String {
        match self.field.as_str() {
            // ID字段不需要引号
            _ if self.field.contains(".id") || self.field.contains("_id") => {
                format!("{} = {}", self.field, self.value)
            }
            // 数值字段不需要引号
            "amount" | "express_fee" | "total_payment" | "platform_fee_total" |
            "sales_price" | "cost_price" | "platform_fee" | "discount" | "quantity" |
            "total_sales_price" | "total_cost_price" | "created_at" | "updated_at" => {
                format!("{} = {}", self.field, self.value)
            }
            // 其他字段需要引号
            _ => format!("{} = '{}'", self.field, self.value),
        }
    }

    pub fn new(field: String, value: String) -> Self {
        UpdateOptions { field, value }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct IdParams {
    pub id: String,
}

#[derive(Deserialize, Debug)]
pub struct CreateParams<D> {
    pub data: D,
}

#[derive(Deserialize, Debug)]
pub struct UpdateParams<D> {
    pub data: D,
}

#[derive(Deserialize, Debug, Clone, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RelateParams {
    pub from: String,
    pub to: Vec<String>,
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ListParams {
    pub page: Option<Page>,
    pub options: Option<ListOptions>,
    pub params: Option<Vec<WhereOptions>>,
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct MessageListParams {
    pub page: Option<Page>,
    pub options: Option<ListOptions>,
    pub req_type: String,
}

#[derive(Default, Serialize, Debug, ToSchema)]
pub struct ListResponse<T: Serialize> {
    pub data: Vec<T>,
    pub total: u32,
    pub page: u32,
    pub size: u32,
}

static GDB: LazyLock<Surreal<Client>> = LazyLock::new(Surreal::init);

pub async fn init_db() -> Result<(), surrealdb::Error> {
    let host = format!("{}:{}", &CFG.database.url, &CFG.database.port);
    let username = &CFG.database.username;
    let password = &CFG.database.password;
    let ns = &CFG.database.db_ns;
    let db_name = &CFG.database.db_name;

    println!("数据库连接信息：{}", host);

    GDB.connect::<Ws>(host).await?;

    GDB.signin(Root {
        username: &username,
        password: &password,
    })
    .await?;

    GDB.use_ns(ns).use_db(db_name).await.unwrap();

    Ok(())
}

pub trait Castable: DeserializeOwned {}
pub trait Creatable: Serialize {}
pub trait Patchable: Serialize {}

#[derive(Debug, Deserialize, Serialize)]
pub struct Record {
    pub id: Thing,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CountRecord {
    pub count: u32,
}

#[derive(Debug, Default, Deserialize, Serialize, Clone)]
pub struct SumForChart {
    #[serde(deserialize_with = "flexible_string_deserializer")]
    pub field: String,
    #[serde(deserialize_with = "flexible_string_deserializer")]
    pub label: String,
    pub value: f64,
}

fn flexible_string_deserializer<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: Deserializer<'de>,
{
    struct FlexibleStringVisitor;

    impl<'de> Visitor<'de> for FlexibleStringVisitor {
        type Value = String;

        fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
            formatter.write_str("string, integer, Thing, or map")
        }

        fn visit_str<E: de::Error>(self, v: &str) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_i64<E: de::Error>(self, v: i64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_u64<E: de::Error>(self, v: u64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_f64<E: de::Error>(self, v: f64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }
    }

    deserializer.deserialize_any(FlexibleStringVisitor)
}
pub struct Database;

#[allow(dead_code)]
impl Database {
    /// 清空数据库中的所有数据
    ///
    /// # 参数
    /// * `table` - 要清空的表名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_clean_db(table: &str) -> AppResult<String> {
        let sql = format!("DELETE {} RETURN NONE;", table);
        GDB.query(sql).await?;
        Ok(format!("表 {} 已清空", table))
    }
    pub async fn exec_query_count(
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<CountRecord>> {
        let mut sql = format!("SELECT COUNT() FROM {}", table);
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                // sql.push_str(&format!("{} = '{}'", param.var, param.val));
                sql.push_str(param.get_sql().as_str());
            }
        }
        sql.push_str(" GROUP ALL;");
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }

    pub async fn exec_query_list<T: Castable>(
        table: &str,
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<T>> {
        let mut sql = format!("SELECT * FROM {} ", table);

        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                // sql.push_str(&format!("{} = '{}'", param.var, param.val));
                sql.push_str(param.get_sql().as_str());
            }
        }

        if let Some(options) = options {
            sql.push_str(" ORDER BY ");

            if let Some(order_by) = options.order_by {
                sql.push_str(&order_by);
            }

            match options.desc {
                Some(true) => sql.push_str(" DESC"),
                Some(false) => sql.push_str(" ASC"),
                None => sql.push_str(" ASC"),
            }
        }

        if limit > 0 {
            sql.push_str(&format!(" LIMIT {} START {}", limit, page));
        };

        sql.push_str(" ;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;

        let res = response.take(0)?;
        Ok(res)
    }

    pub async fn exec_get_by_id<T: Castable>(table: &str, tid: &str) -> AppResult<Option<T>> {
        let parts: Vec<&str> = tid.split(':').collect();
        if parts.len() == 2 {
            let res = GDB.select((table, parts[1])).await?;
            return Ok(res);
        }
        let res = GDB.select((table, tid)).await?;
        Ok(res)
    }

    pub async fn exec_get_by_query<T: Castable>(
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<T>> {
        let mut sql = format!("SELECT * FROM {} WHERE ", table);
        for (i, param) in params.iter().enumerate() {
            if i > 0 {
                sql.push_str(" AND ");
            }
            // sql.push_str(&format!("{} = '{}'", param.var, param.val));
            sql.push_str(param.get_sql().as_str());
        }

        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);
        let mut ress = GDB.query(sql).await?;
        let res = ress.take(0)?;
        Ok(res)
    }

    pub async fn exec_get_field_by_query<T: Castable>(
        field: &str,
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<T>> {
        let mut sql = format!("SELECT {} FROM {} WHERE ", field, table);
        for (i, param) in params.iter().enumerate() {
            if i > 0 {
                sql.push_str(" AND ");
            }
            // sql.push_str(&format!("{} = '{}'", param.var, param.val));
            sql.push_str(param.get_sql().as_str());
        }

        sql.push_str(" LIMIT 1;");
        let mut ress = GDB.query(sql).await?;
        let res = ress.take(0)?;
        Ok(res)
    }

    pub async fn exec_create<T: Creatable + 'static>(table: &str, obj: T) -> AppResult<String> {
        let ress: Option<Record> = GDB.create(table).content(obj).await?;
        match ress {
            Some(res) => Ok(res.id.to_raw()),
            None => Err(anyhow::anyhow!("create failed").into()),
        }
    }

    pub async fn exec_update<T: Patchable + 'static>(
        table: &str,
        tid: &str,
        obj: T,
    ) -> AppResult<String> {
        let final_tid = if tid.to_string().contains(':') {
            tid.to_string()
                .split(':')
                .nth(1)
                .unwrap_or(&tid)
                .to_string()
        } else {
            tid.to_string().clone()
        };
        let res: Option<Record> = GDB.update((table, &final_tid)).merge(obj).await?;
        match res {
            Some(_) => Ok(tid.to_string()),
            None => Err(anyhow::anyhow!("update failed, table: {}, tid: {}", table, tid).into()),
        }
    }

    pub async fn exec_delete(table: &str, tid: String) -> AppResult<String> {
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };

        let res: Option<Record> = GDB.delete((table, &final_tid)).await?;
        match res {
            Some(_) => Ok(tid),
            None => Err(anyhow::anyhow!("delete failed, table: {}, tid: {}", table, tid).into()),
        }
    }

    // from -> table -> to 对应 in -> table -> out的结构
    pub async fn exec_relate(table: &str, from: &str, to: &str) -> AppResult<String> {
        let sql = format!("RELATE {}->{}->{};", from, table, to);
        let res: Option<Record> = GDB.query(sql).await?.take(0)?;
        match res {
            Some(_) => Ok(format!("{from} -> {table} -> {to}")),
            None => Err(anyhow::anyhow!(
                "relate failed, table: {}, from: {}, to: {}",
                table,
                from,
                to
            )
            .into()),
        }
    }

    // from -> table -> to 对应 in -> table -> out的结构
    pub async fn exec_relate_batch(
        table: &str,
        from: Vec<String>,
        to: Vec<String>,
    ) -> AppResult<String> {
        let from_id = if from.len() == 1 {
            from[0].clone()
        } else {
            format!("[{}]", from.join(","))
        };

        let to_id = if to.len() == 1 {
            to[0].clone()
        } else {
            format!("[{}]", to.join(","))
        };
        let sql = format!("RELATE {}->{}->{};", from_id, table, to_id);
        GDB.query(sql).await?;
        Ok("Items has been related !".to_string())
    }

    pub async fn exec_query_relate<T: Castable>(
        table: &str,
        page: u32,
        limit: u32,
        state: &str,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<T>> {
        let mut sql = format!("SELECT *, {} FROM {} ", state, table);
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                // sql.push_str(&format!("{} = '{}'", param.var, param.val));
                sql.push_str(param.get_sql().as_str());
            }
        }

        if let Some(options) = options {
            sql.push_str(" ORDER BY ");

            if let Some(order_by) = options.order_by {
                sql.push_str(&order_by);
            } else {
                sql.push_str("create_at");
            }

            if let Some(_desc) = options.desc {
                sql.push_str(" DESC");
            } else {
                sql.push_str(" ASC");
            }
        }

        if limit > 0 {
            sql.push_str(&format!(" LIMIT {} START {}", limit, page));
        };

        sql.push_str(" ;");
        println!("SQL语句：{}", sql);
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }

    pub async fn exec_return_relate(table: &str, from: &str, to: &str) -> AppResult<Vec<String>> {
        let sql = format!("RETURN {}->{}->{}", from, table, to);
        let mut response = GDB.query(sql).await?;
        let list: Vec<Thing> = response.take(0)?;
        let mut res = Vec::new();
        for item in list {
            res.push(item.to_raw());
        }
        Ok(res)
    }

    pub async fn exec_unrelate(table: &str, from: &str, to: &str) -> AppResult<String> {
        let sql = format!("DELETE {from}->{table} WHERE out={to} RETURN BEFORE;");
        let res: Option<Record> = GDB.query(sql).await?.take(0)?;
        match res {
            Some(_) => Ok(format!("{from} -> {table} -> {to}")),
            None => Err(anyhow::anyhow!(
                "delete relate failed, table: {}, from: {}, to: {}",
                table,
                from,
                to
            )
            .into()),
        }
    }

    /// 根据查询条件更新单条记录
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_update_by_query(
        table: &str,
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        let mut sql = format!("UPDATE {} SET {} = '{}'", table, update_field, update_value);
        
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }
        
        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);
        
        let mut response = GDB.query(sql).await?;
        let res: Option<Record> = response.take(0)?;
        
        match res {
            Some(record) => Ok(record.id.to_raw()),
            None => Err(anyhow::anyhow!("update by query failed, table: {}, field: {}", table, update_field).into()),
        }
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_update_multiple_fields_by_query(
        table: &str,
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        if update_fields.is_empty() {
            return Err(anyhow::anyhow!("update fields cannot be empty").into());
        }

        let mut sql = format!("UPDATE {} SET ", table);

        // 构建SET子句
        for (i, field) in update_fields.iter().enumerate() {
            if i > 0 {
                sql.push_str(", ");
            }
            sql.push_str(&field.get_sql());
        }

        // 构建WHERE子句
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let res: Option<Record> = response.take(0)?;

        match res {
            Some(record) => Ok(record.id.to_raw()),
            None => Err(anyhow::anyhow!("update multiple fields by query failed, table: {}", table).into()),
        }
    }

    /// 根据查询条件对指定字段进行求和统计
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件
    /// * `count_field` - 要进行求和的字段名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回求和结果或错误信息
    ///
    /// # 功能
    /// - 通过SQL测试字段是否为可汇总类型（数值类型）
    /// - 如果字段不可汇总，返回错误
    /// - 如果可以汇总，执行SQL求和并返回结果
    pub async fn exec_sum(
        table: &str,
        params: Vec<WhereOptions>,
        count_field: &str,
    ) -> AppResult<String> {
        // 首先通过SQL测试字段是否可以进行汇总
        // 构建测试SQL：尝试对字段进行求和操作，限制返回1条记录以提高性能
        let test_sql = format!("SELECT math::sum({}) AS test_sum FROM {} LIMIT 1;", count_field, table);
        println!("字段类型测试SQL：{}", test_sql);

        // 执行测试查询
        let mut test_response = GDB.query(test_sql).await;

        match test_response {
            Ok(ref mut response) => {
                // 尝试获取测试结果
                let test_result: Result<Option<serde_json::Value>, _> = response.take(0);

                match test_result {
                    Ok(_) => {
                        // 测试成功，说明字段可以进行汇总
                        println!("字段 '{}' 类型验证通过，可以进行汇总", count_field);
                    }
                    Err(e) => {
                        // 测试失败，说明字段不能进行汇总
                        return Err(anyhow::anyhow!(
                            "字段 '{}' 不是可汇总的数值类型字段。SurrealDB错误: {}。请确保字段存在且为数值类型（如：整数、浮点数、Decimal等）。",
                            count_field,
                            e
                        ).into());
                    }
                }
            }
            Err(e) => {
                // 查询执行失败
                return Err(anyhow::anyhow!(
                    "无法验证字段 '{}' 的类型。可能原因：1) 字段不存在，2) 表不存在，3) 字段不是数值类型。SurrealDB错误: {}",
                    count_field,
                    e
                ).into());
            }
        }

        // 字段验证通过，构建实际的求和SQL查询语句
        let mut sql = format!("SELECT math::sum({}) AS sum_result FROM {}", count_field, table);

        // 添加WHERE条件
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(";");
        println!("实际求和SQL语句：{}", sql);

        // 执行实际的求和查询
        let mut response = GDB.query(sql).await?;

        // 尝试获取结果
        let result: Option<serde_json::Value> = response.take(0)?;

        match result {
            Some(value) => {
                // 从结果中提取sum_result字段
                if let Some(sum_value) = value.get("sum_result") {
                    match sum_value {
                        serde_json::Value::Number(num) => {
                            Ok(format!("字段 '{}' 的汇总结果: {}", count_field, num))
                        }
                        serde_json::Value::Null => {
                            Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据或字段值为空)", count_field))
                        }
                        _ => {
                            Err(anyhow::anyhow!(
                                "字段 '{}' 返回了非数值类型的结果，无法进行汇总",
                                count_field
                            ).into())
                        }
                    }
                } else {
                    Err(anyhow::anyhow!(
                        "查询结果中未找到汇总字段，可能字段 '{}' 不存在",
                        count_field
                    ).into())
                }
            }
            None => {
                Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据)", count_field))
            }
        }
    }

    /// 根据查询条件对指定字段进行求和统计（快速版本，跳过预验证）
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件
    /// * `count_field` - 要进行求和的字段名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回求和结果或错误信息
    ///
    /// # 功能
    /// - 直接执行求和查询，不进行预先的字段类型验证
    /// - 性能更高，但错误信息可能不如标准版本详细
    /// - 适用于已知字段类型正确的场景
    pub async fn exec_sum_fast(
        table: &str,
        params: Vec<WhereOptions>,
        count_field: &str,
    ) -> AppResult<String> {
        // 直接构建求和SQL查询语句，不进行预验证
        let mut sql = format!("SELECT math::sum({}) AS sum_result FROM {}", count_field, table);

        // 添加WHERE条件
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(";");
        println!("快速求和SQL语句：{}", sql);

        // 执行求和查询
        let mut response = GDB.query(sql).await;

        match response {
            Ok(ref mut resp) => {
                // 尝试获取结果
                let result: Result<Option<serde_json::Value>, _> = resp.take(0);

                match result {
                    Ok(Some(value)) => {
                        // 从结果中提取sum_result字段
                        if let Some(sum_value) = value.get("sum_result") {
                            match sum_value {
                                serde_json::Value::Number(num) => {
                                    Ok(format!("字段 '{}' 的汇总结果: {}", count_field, num))
                                }
                                serde_json::Value::Null => {
                                    Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据或字段值为空)", count_field))
                                }
                                _ => {
                                    Err(anyhow::anyhow!(
                                        "字段 '{}' 返回了非数值类型的结果，无法进行汇总",
                                        count_field
                                    ).into())
                                }
                            }
                        } else {
                            Err(anyhow::anyhow!(
                                "查询结果中未找到汇总字段，可能字段 '{}' 不存在",
                                count_field
                            ).into())
                        }
                    }
                    Ok(None) => {
                        Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据)", count_field))
                    }
                    Err(e) => {
                        Err(anyhow::anyhow!(
                            "字段 '{}' 求和失败。可能原因：1) 字段不存在，2) 字段不是数值类型，3) SQL语法错误。SurrealDB错误: {}",
                            count_field,
                            e
                        ).into())
                    }
                }
            }
            Err(e) => {
                Err(anyhow::anyhow!(
                    "执行求和查询失败。表: '{}', 字段: '{}'。SurrealDB错误: {}",
                    table,
                    count_field,
                    e
                ).into())
            }
        }
    }

    // 函数限制：必须按年份查询，固定输入年份
    pub async fn exec_summary_by_month(
        table: &str,
        state: &str,
        label: &str,
        year: i32,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<SumForChart>> {
        let mut sql = format!(
            "
            SELECT 
                {} AS field, 
                {} AS label,
                math::sum(amount) AS value 
            FROM {} 
            WHERE time::year(<datetime>date) = {}",
            state, label, table, year
        );
        if params.len() > 0 {
            sql.push_str(" AND ");
            for param in params.iter() {
                sql.push_str(param.get_sql().as_str());
            }
        }
        sql.push_str(
            "
            GROUP BY 
                field, label
            ORDER BY 
                field, label ASC;",
        );
        println!("SQL语句：{}", sql);
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }
}
