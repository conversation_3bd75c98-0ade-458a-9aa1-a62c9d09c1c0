use std::str::FromStr;

use crate::app_error::AppError;
use crate::app_writer::AppResult;
use crate::db::{Castable, CountRecord, Creatable, Database, ListOptions, Patchable, UpdateOptions, WhereOptions};
use crate::dtos::sales_order::{SalesOrderCreate, SalesOrderResponse, SalesOrderUpdate};
use anyhow::anyhow;
use chrono::Local;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct SalesOrder {
    pub id: Option<RecordId>,
    pub status: Option<String>,
    pub creator_id: Option<RecordId>,
    pub updater_id: Option<RecordId>,
    pub serial: String,
    // 关联合同
    pub contract_id: Option<RecordId>,
    // 关联款项使用批次
    pub repayment_id: Option<RecordId>,
    // 导入批次
    pub import_record: Option<String>,
    // 下单时间
    pub purchase_time: Option<String>,
    // 支付时间
    pub pay_time: Option<String>,
    pub pay_type: Option<String>,
    pub pay_info: Option<String>,
    pub customer: Option<String>,
    pub receive_phone: Option<String>,
    pub customer_phone: Option<String>,
    pub address: Option<String>,
    pub express_type: Option<String>,
    pub express_company: Option<String>,
    pub express_order: Option<String>,
    pub platform_name: Option<String>,
    pub platform_serial: Option<String>,
    pub platform_order_serial: Option<String>,
    pub platform_fee_total: Decimal,
    // 商品总金额
    pub amount: Decimal,
    // 运输费用
    pub express_fee: Decimal,
    // 总金额
    pub total_payment: Decimal,
    // 发货时间
    pub delivery_time: Option<String>,
    // 签收时间
    pub sign_time: Option<String>,
    // 订单完成时间
    pub complete_time: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for SalesOrder {}
impl Patchable for SalesOrder {}
impl Castable for SalesOrder {}

impl SalesOrder {
    pub async fn response(self) -> SalesOrderResponse {
        let creator_id = self.creator_id.map(|id| id.to_string());
        let updater_id = self.updater_id.map(|id| id.to_string());
        let contract_id = self.contract_id.map(|id| id.to_string());
        let repayment_id = self.repayment_id.map(|id| id.to_string());

        SalesOrderResponse {
            id: self.id.unwrap().to_string(),
            status: self.status,
            creator_id,
            updater_id,
            contract_id,
            repayment_id,
            serial: self.serial,
            import_record: self.import_record,
            purchase_time: self.purchase_time,
            pay_time: self.pay_time,
            pay_type: self.pay_type,
            pay_info: self.pay_info,
            customer: self.customer,
            receive_phone: self.receive_phone,
            customer_phone: self.customer_phone,
            address: self.address,
            express_type: self.express_type,
            express_company: self.express_company,
            express_order: self.express_order,
            platform_name: self.platform_name,
            platform_serial: self.platform_serial,
            platform_order_serial: self.platform_order_serial,
            platform_fee_total: self.platform_fee_total,
            amount: self.amount,
            express_fee: self.express_fee,
            total_payment: self.total_payment,
            delivery_time: self.delivery_time,
            sign_time: self.sign_time,
            complete_time: self.complete_time,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(sales_order: SalesOrderCreate) -> SalesOrder {
        let time_now = Local::now().timestamp_millis();
        let creator_id = sales_order
            .creator_id
            .map(|id| RecordId::from_str(&id).unwrap());
        let updater_id = sales_order
            .updater_id
            .map(|id| RecordId::from_str(&id).unwrap());

        SalesOrder {
            id: None,
            status: sales_order.status,
            creator_id,
            updater_id,
            contract_id: sales_order
                .contract_id
                .map(|id| RecordId::from_str(&id).unwrap()),
            repayment_id: sales_order
                .repayment_id
                .map(|id| RecordId::from_str(&id).unwrap()),
            serial: sales_order.serial,
            import_record: sales_order.import_record,
            purchase_time: sales_order.purchase_time,
            pay_time: sales_order.pay_time,
            pay_type: sales_order.pay_type,
            pay_info: sales_order.pay_info,
            customer: sales_order.customer,
            receive_phone: sales_order.receive_phone,
            customer_phone: sales_order.customer_phone,
            address: sales_order.address,
            express_type: sales_order.express_type,
            express_company: sales_order.express_company,
            express_order: sales_order.express_order,
            platform_name: sales_order.platform_name,
            platform_serial: sales_order.platform_serial,
            platform_order_serial: sales_order.platform_order_serial,
            platform_fee_total: sales_order.platform_fee_total,
            amount: sales_order.amount,
            express_fee: sales_order.express_fee,
            total_payment: sales_order.total_payment,
            delivery_time: sales_order.delivery_time,
            sign_time: sales_order.sign_time,
            complete_time: sales_order.complete_time,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: SalesOrderUpdate, old: SalesOrder) -> SalesOrder {
        let time_now = Local::now().timestamp_millis();
        let creator_id = new.creator_id.map(|id| RecordId::from_str(&id).unwrap());
        let updater_id = new.updater_id.map(|id| RecordId::from_str(&id).unwrap());

        SalesOrder {
            id: old.id.clone(),
            status: new.status,
            creator_id,
            updater_id,
            contract_id: new.contract_id.map(|id| RecordId::from_str(&id).unwrap()),
            repayment_id: new.repayment_id.map(|id| RecordId::from_str(&id).unwrap()),
            serial: new.serial,
            import_record: new.import_record,
            purchase_time: new.purchase_time,
            pay_time: new.pay_time,
            pay_type: new.pay_type,
            pay_info: new.pay_info,
            customer: new.customer,
            receive_phone: new.receive_phone,
            customer_phone: new.customer_phone,
            address: new.address,
            express_type: new.express_type,
            express_company: new.express_company,
            express_order: new.express_order,
            platform_name: new.platform_name,
            platform_serial: new.platform_serial,
            platform_order_serial: new.platform_order_serial,
            platform_fee_total: new.platform_fee_total,
            amount: new.amount,
            express_fee: new.express_fee,
            total_payment: new.total_payment,
            delivery_time: new.delivery_time,
            sign_time: new.sign_time,
            complete_time: new.complete_time,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }

    /// 将 SalesOrderUpdate 转换为 UpdateOptions 列表
    ///
    /// 这个方法负责将更新数据转换为数据库更新字段列表，
    /// 只包含实际需要更新的字段，提高更新效率
    ///
    /// # 参数
    /// * `update_data` - 要更新的销售订单数据
    ///
    /// # 返回值
    /// * `Vec<UpdateOptions>` - 数据库更新字段列表
    pub fn to_update_options(update_data: SalesOrderUpdate) -> Vec<UpdateOptions> {
        let mut update_fields = Vec::new();
        let time_now = Local::now().timestamp_millis();

        // 可选字段 - 只有当值存在时才添加到更新列表
        if let Some(status) = update_data.status {
            update_fields.push(UpdateOptions::new("status".to_string(), status));
        }
        if let Some(creator_id) = update_data.creator_id {
            update_fields.push(UpdateOptions::new("creator_id".to_string(), creator_id));
        }
        if let Some(updater_id) = update_data.updater_id {
            update_fields.push(UpdateOptions::new("updater_id".to_string(), updater_id));
        }
        if let Some(contract_id) = update_data.contract_id {
            update_fields.push(UpdateOptions::new("contract_id".to_string(), contract_id));
        }
        if let Some(repayment_id) = update_data.repayment_id {
            update_fields.push(UpdateOptions::new("repayment_id".to_string(), repayment_id));
        }
        if let Some(import_record) = update_data.import_record {
            update_fields.push(UpdateOptions::new("import_record".to_string(), import_record));
        }
        if let Some(purchase_time) = update_data.purchase_time {
            update_fields.push(UpdateOptions::new("purchase_time".to_string(), purchase_time));
        }
        if let Some(pay_time) = update_data.pay_time {
            update_fields.push(UpdateOptions::new("pay_time".to_string(), pay_time));
        }
        if let Some(pay_type) = update_data.pay_type {
            update_fields.push(UpdateOptions::new("pay_type".to_string(), pay_type));
        }
        if let Some(pay_info) = update_data.pay_info {
            update_fields.push(UpdateOptions::new("pay_info".to_string(), pay_info));
        }
        if let Some(customer) = update_data.customer {
            update_fields.push(UpdateOptions::new("customer".to_string(), customer));
        }
        if let Some(receive_phone) = update_data.receive_phone {
            update_fields.push(UpdateOptions::new("receive_phone".to_string(), receive_phone));
        }
        if let Some(customer_phone) = update_data.customer_phone {
            update_fields.push(UpdateOptions::new("customer_phone".to_string(), customer_phone));
        }
        if let Some(address) = update_data.address {
            update_fields.push(UpdateOptions::new("address".to_string(), address));
        }
        if let Some(express_type) = update_data.express_type {
            update_fields.push(UpdateOptions::new("express_type".to_string(), express_type));
        }
        if let Some(express_company) = update_data.express_company {
            update_fields.push(UpdateOptions::new("express_company".to_string(), express_company));
        }
        if let Some(express_order) = update_data.express_order {
            update_fields.push(UpdateOptions::new("express_order".to_string(), express_order));
        }
        if let Some(platform_name) = update_data.platform_name {
            update_fields.push(UpdateOptions::new("platform_name".to_string(), platform_name));
        }
        if let Some(platform_serial) = update_data.platform_serial {
            update_fields.push(UpdateOptions::new("platform_serial".to_string(), platform_serial));
        }
        if let Some(platform_order_serial) = update_data.platform_order_serial {
            update_fields.push(UpdateOptions::new("platform_order_serial".to_string(), platform_order_serial));
        }
        if let Some(delivery_time) = update_data.delivery_time {
            update_fields.push(UpdateOptions::new("delivery_time".to_string(), delivery_time));
        }
        if let Some(sign_time) = update_data.sign_time {
            update_fields.push(UpdateOptions::new("sign_time".to_string(), sign_time));
        }
        if let Some(complete_time) = update_data.complete_time {
            update_fields.push(UpdateOptions::new("complete_time".to_string(), complete_time));
        }

        // 必需字段 - 总是添加到更新列表
        update_fields.push(UpdateOptions::new("serial".to_string(), update_data.serial));
        update_fields.push(UpdateOptions::new("platform_fee_total".to_string(), update_data.platform_fee_total.to_string()));
        update_fields.push(UpdateOptions::new("amount".to_string(), update_data.amount.to_string()));
        update_fields.push(UpdateOptions::new("express_fee".to_string(), update_data.express_fee.to_string()));
        update_fields.push(UpdateOptions::new("total_payment".to_string(), update_data.total_payment.to_string()));

        // 自动更新时间戳
        update_fields.push(UpdateOptions::new("updated_at".to_string(), time_now.to_string()));

        update_fields
    }
}

pub struct SalesOrderBmc;

impl SalesOrderBmc {
    const ENTITY: &'static str = "sales_order";

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<SalesOrder>> {
        println!("传入参数：：：{:?}, {:?}", page, limit);
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<SalesOrder>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<SalesOrder>> {
        Database::exec_get_by_id(Self::ENTITY, &id).await
    }

    pub async fn create(sales_order: SalesOrderCreate) -> AppResult<String> {
        let obj = SalesOrder::create(sales_order);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn create_or_update(sales_order: SalesOrderCreate) -> AppResult<String> {
        if sales_order.platform_order_serial.is_none() {
            return Err(anyhow!("平台订单号缺失，无法导入.").into());
        }
        let check_params = vec![WhereOptions {
            var: "platform_order_serial".to_string(),
            val: sales_order.platform_order_serial.clone().unwrap(),
        }];
        let check: Option<SalesOrder> =
            Database::exec_get_by_query(Self::ENTITY, check_params).await?;
        if check.is_none() {
            let obj = SalesOrder::create(sales_order);
            let res = Database::exec_create(Self::ENTITY, obj).await?;
            return Ok(res);
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        Database::exec_update(Self::ENTITY, &final_tid, sales_order).await
    }

    pub async fn update(sales_order: SalesOrderUpdate) -> AppResult<String> {
        let check: Option<SalesOrder> =
            Database::exec_get_by_id(Self::ENTITY, &sales_order.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("SalesOrder not found.")));
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid
        };
        let obj = SalesOrder::update(sales_order, old);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    /// 优化版本的更新方法，使用多字段更新功能
    ///
    /// # 参数
    /// * `sales_order` - 要更新的销售订单数据
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 优势
    /// - 使用单次SQL操作更新所有字段
    /// - 避免了完整对象的序列化和反序列化
    /// - 提高了性能，特别是在网络延迟较高的环境中
    /// - 字段处理逻辑集中在实体层，便于管理
    pub async fn update_optimized(sales_order: SalesOrderUpdate) -> AppResult<String> {
        // 检查记录是否存在
        let check: Option<SalesOrder> =
            Database::exec_get_by_id(Self::ENTITY, &sales_order.id.clone()).await?;
        if check.is_none() {
            return Err(AppError::AnyHow(anyhow!("SalesOrder not found.")));
        }

        // 构建查询条件
        let params = vec![WhereOptions::new("id".to_string(), sales_order.id.clone())];

        // 使用实体层的方法转换为更新字段列表
        let update_fields = SalesOrder::to_update_options(sales_order);

        // 执行多字段更新
        Self::update_multiple_fields(params, update_fields).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    /// 根据查询条件更新单个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let result = SalesOrderBmc::update_field(params, "status", "completed").await?;
    /// ```
    pub async fn update_field(
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        Database::exec_update_by_query(Self::ENTITY, params, update_field, update_value).await
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let update_fields = vec![
    ///     UpdateOptions::new("status".to_string(), "completed".to_string()),
    ///     UpdateOptions::new("amount".to_string(), "1000.00".to_string()),
    /// ];
    /// let result = SalesOrderBmc::update_multiple_fields(params, update_fields).await?;
    /// ```
    pub async fn update_multiple_fields(
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        Database::exec_update_multiple_fields_by_query(Self::ENTITY, params, update_fields).await
    }

    /// 根据查询条件对指定字段进行求和统计
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `count_field` - 要进行求和的字段名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回求和结果
    ///
    /// # 示例
    /// ```rust
    /// // 统计所有已完成订单的总金额
    /// let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
    /// let result = SalesOrderBmc::sum_field(params, "total_payment").await?;
    ///
    /// // 统计特定合同下的订单总金额
    /// let params = vec![WhereOptions::new("contract_id".to_string(), "contract:123".to_string())];
    /// let result = SalesOrderBmc::sum_field(params, "amount").await?;
    /// ```
    pub async fn sum_field(
        params: Vec<WhereOptions>,
        count_field: &str,
    ) -> AppResult<String> {
        Database::exec_sum(Self::ENTITY, params, count_field).await
    }

    /// 根据查询条件对指定字段进行求和统计（快速版本）
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `count_field` - 要进行求和的字段名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回求和结果
    ///
    /// # 特点
    /// - 跳过字段类型预验证，直接执行求和查询
    /// - 性能更高，适用于已知字段类型正确的场景
    /// - 错误信息可能不如标准版本详细
    ///
    /// # 示例
    /// ```rust
    /// // 快速统计订单总金额（已知字段类型正确）
    /// let params = vec![WhereOptions::new("status".to_string(), "completed".to_string())];
    /// let result = SalesOrderBmc::sum_field_fast(params, "total_payment").await?;
    /// ```
    pub async fn sum_field_fast(
        params: Vec<WhereOptions>,
        count_field: &str,
    ) -> AppResult<String> {
        Database::exec_sum_fast(Self::ENTITY, params, count_field).await
    }
}
