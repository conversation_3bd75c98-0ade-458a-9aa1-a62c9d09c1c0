// 多字段更新功能使用示例
// 这个文件展示了如何使用新的多字段更新功能

use crate::{
    db::{UpdateOptions, WhereOptions},
    services::sales_order::SalesOrderService,
};

/// 示例：使用多字段更新功能
pub async fn example_update_multiple_fields() {
    // 示例1：同时更新订单状态和金额
    let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    let update_fields = vec![
        UpdateOptions::new("status".to_string(), "completed".to_string()),
        UpdateOptions::new("amount".to_string(), "1500.00".to_string()),
        UpdateOptions::new("total_payment".to_string(), "1500.00".to_string()),
    ];
    
    match SalesOrderService::update_multiple_fields(params, update_fields).await {
        Ok(result) => println!("多字段更新成功: {}", result),
        Err(e) => println!("多字段更新失败: {}", e),
    }

    // 示例2：使用专门的金额字段更新方法
    let params = vec![WhereOptions::new("id".to_string(), "sales_order:123".to_string())];
    match SalesOrderService::update_amount_fields(params, "2000.00", "2000.00").await {
        Ok(result) => println!("金额字段更新成功: {}", result),
        Err(e) => println!("金额字段更新失败: {}", e),
    }

    // 示例3：单字段更新（原有功能）
    let params = vec![WhereOptions::new("platform_order_serial".to_string(), "PO123456".to_string())];
    match SalesOrderService::update_field(params, "status", "shipped").await {
        Ok(result) => println!("单字段更新成功: {}", result),
        Err(e) => println!("单字段更新失败: {}", e),
    }
}

/// 性能对比示例
pub async fn performance_comparison_example() {
    let params = vec![WhereOptions::new("serial".to_string(), "SO002".to_string())];
    
    // 旧方式：分别调用两次单字段更新
    println!("=== 旧方式：分别更新两个字段 ===");
    let start = std::time::Instant::now();
    
    if let Err(e) = SalesOrderService::update_field(params.clone(), "amount", "1000.00").await {
        println!("更新amount失败: {}", e);
    }
    if let Err(e) = SalesOrderService::update_field(params.clone(), "total_payment", "1000.00").await {
        println!("更新total_payment失败: {}", e);
    }
    
    let old_duration = start.elapsed();
    println!("旧方式耗时: {:?}", old_duration);

    // 新方式：一次性更新多个字段
    println!("=== 新方式：一次性更新多个字段 ===");
    let start = std::time::Instant::now();
    
    let update_fields = vec![
        UpdateOptions::new("amount".to_string(), "1200.00".to_string()),
        UpdateOptions::new("total_payment".to_string(), "1200.00".to_string()),
    ];
    
    if let Err(e) = SalesOrderService::update_multiple_fields(params, update_fields).await {
        println!("多字段更新失败: {}", e);
    }
    
    let new_duration = start.elapsed();
    println!("新方式耗时: {:?}", new_duration);
    
    if old_duration > new_duration {
        println!("性能提升: {:?}", old_duration - new_duration);
    }
}

/// SQL生成示例
pub fn sql_generation_example() {
    println!("=== SQL生成示例 ===");
    
    // 单字段更新SQL
    println!("单字段更新SQL:");
    println!("UPDATE sales_order SET status = 'completed' WHERE serial = 'SO001' LIMIT 1;");
    
    // 多字段更新SQL
    println!("\n多字段更新SQL:");
    println!("UPDATE sales_order SET status = 'completed', amount = '1500.00', total_payment = '1500.00' WHERE serial = 'SO001' LIMIT 1;");
    
    // 复杂条件的多字段更新SQL
    println!("\n复杂条件的多字段更新SQL:");
    println!("UPDATE sales_order SET status = 'shipped', delivery_time = '2024-01-15 10:30:00' WHERE contract_id = 'contract:123' AND platform_order_serial = 'PO123456' LIMIT 1;");
}
